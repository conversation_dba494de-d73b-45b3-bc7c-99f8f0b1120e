// ==UserScript==
// @name         Augment Code 地区限制绕过器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  绕过 Augment Code 的地区注册限制
// <AUTHOR>
// @match        https://www.augmentcode.com/*
// @match        https://login.augmentcode.com/*
// @match        https://auth.augmentcode.com/*
// @match        https://app.augmentcode.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 Augment Code 地区限制绕过器已启动');
    
    // 配置
    const CONFIG = {
        fakeIP: '*******',
        fakeCountry: 'US',
        fakeLanguage: 'en-US',
        fakeTimezone: 'America/New_York',
        fakeCoords: {
            latitude: 40.7128,
            longitude: -74.0060,
            accuracy: 10
        }
    };
    
    // 1. 伪装地理位置API
    function spoofGeolocation() {
        if (navigator.geolocation) {
            const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
            const originalWatchPosition = navigator.geolocation.watchPosition;
            
            navigator.geolocation.getCurrentPosition = function(success, error, options) {
                console.log('🌍 拦截地理位置请求，返回美国坐标');
                if (success) {
                    success({
                        coords: CONFIG.fakeCoords,
                        timestamp: Date.now()
                    });
                }
            };
            
            navigator.geolocation.watchPosition = function(success, error, options) {
                console.log('🌍 拦截地理位置监听，返回美国坐标');
                if (success) {
                    success({
                        coords: CONFIG.fakeCoords,
                        timestamp: Date.now()
                    });
                }
                return 1; // 返回假的watchId
            };
        }
    }
    
    // 2. 伪装浏览器属性
    function spoofNavigatorProperties() {
        // 伪装语言
        Object.defineProperty(navigator, 'language', {
            value: CONFIG.fakeLanguage,
            writable: false,
            configurable: false
        });
        
        Object.defineProperty(navigator, 'languages', {
            value: [CONFIG.fakeLanguage, 'en'],
            writable: false,
            configurable: false
        });
        
        // 伪装时区
        try {
            Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                value: function() {
                    return {
                        ...Intl.DateTimeFormat.prototype.resolvedOptions.call(this),
                        timeZone: CONFIG.fakeTimezone
                    };
                }
            });
        } catch (e) {
            console.log('⚠️ 时区伪装失败:', e);
        }
        
        console.log('🎭 浏览器属性伪装完成');
    }
    
    // 3. 拦截并修改网络请求
    function interceptNetworkRequests() {
        // 拦截 fetch 请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = typeof args[0] === 'string' ? args[0] : args[0].url;
            const options = args[1] || {};
            
            // 添加伪装头部
            if (!options.headers) {
                options.headers = {};
            }
            
            // 添加IP和地区伪装头
            options.headers['X-Forwarded-For'] = CONFIG.fakeIP;
            options.headers['CF-IPCountry'] = CONFIG.fakeCountry;
            options.headers['X-Real-IP'] = CONFIG.fakeIP;
            options.headers['X-Country-Code'] = CONFIG.fakeCountry;
            options.headers['Accept-Language'] = CONFIG.fakeLanguage + ',en;q=0.9';
            
            console.log('📡 拦截 fetch 请求:', url);
            return originalFetch.apply(this, [args[0], options]);
        };
        
        // 拦截 XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._url = url;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            // 添加伪装头部
            this.setRequestHeader('X-Forwarded-For', CONFIG.fakeIP);
            this.setRequestHeader('CF-IPCountry', CONFIG.fakeCountry);
            this.setRequestHeader('X-Real-IP', CONFIG.fakeIP);
            this.setRequestHeader('X-Country-Code', CONFIG.fakeCountry);
            this.setRequestHeader('Accept-Language', CONFIG.fakeLanguage + ',en;q=0.9');
            
            console.log('📡 拦截 XHR 请求:', this._url);
            return originalXHRSend.apply(this, [data]);
        };
        
        console.log('🔄 网络请求拦截设置完成');
    }
    
    // 4. 清除地区检测缓存
    function clearRegionCache() {
        try {
            const keysToRemove = [
                'user_region', 'detected_country', 'geo_location', 
                'country_code', 'region_blocked', 'location_data',
                'augment_region', 'user_country'
            ];
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });
            
            console.log('🗑️ 地区缓存清除完成');
        } catch (e) {
            console.log('⚠️ 缓存清除失败:', e);
        }
    }
    
    // 5. 监听并修改DOM
    function interceptDOMChanges() {
        // 监听地区限制相关的DOM元素
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // 查找并移除地区限制提示
                        const regionBlocks = node.querySelectorAll ? 
                            node.querySelectorAll('[class*="region"], [class*="blocked"], [class*="restricted"]') : [];
                        
                        regionBlocks.forEach(el => {
                            if (el.textContent && el.textContent.includes('region')) {
                                console.log('🚫 移除地区限制元素:', el);
                                el.remove();
                            }
                        });
                        
                        // 查找包含地区限制文本的元素
                        if (node.textContent && 
                            (node.textContent.includes('limiting signups in certain regions') ||
                             node.textContent.includes('region') ||
                             node.textContent.includes('not available in your location'))) {
                            console.log('🚫 隐藏地区限制消息:', node);
                            node.style.display = 'none';
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body || document.documentElement, {
            childList: true,
            subtree: true
        });
        
        console.log('👁️ DOM监听器已启动');
    }
    
    // 6. 注入CSS隐藏地区限制元素
    function injectCSS() {
        const style = document.createElement('style');
        style.textContent = `
            /* 隐藏可能的地区限制元素 */
            [class*="region-block"],
            [class*="geo-block"],
            [class*="location-restricted"],
            [id*="region-warning"],
            [id*="geo-warning"] {
                display: none !important;
            }
            
            /* 隐藏包含特定文本的元素 */
            *:contains("limiting signups in certain regions"),
            *:contains("not available in your region"),
            *:contains("region restriction") {
                display: none !important;
            }
        `;
        
        (document.head || document.documentElement).appendChild(style);
        console.log('🎨 CSS注入完成');
    }
    
    // 7. 绕过前端JavaScript检测
    function bypassJavaScriptDetection() {
        // 伪装WebRTC IP检测
        if (window.RTCPeerConnection) {
            const originalRTC = window.RTCPeerConnection;
            window.RTCPeerConnection = function(...args) {
                const pc = new originalRTC(...args);
                const originalCreateDataChannel = pc.createDataChannel;
                pc.createDataChannel = function() {
                    console.log('🔄 拦截WebRTC数据通道创建');
                    return originalCreateDataChannel.apply(this, arguments);
                };
                return pc;
            };
        }

        // 伪装Canvas指纹
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
            console.log('🎨 拦截Canvas指纹检测');
            return originalToDataURL.apply(this, arguments);
        };

        // 伪装WebGL指纹
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === this.RENDERER || parameter === this.VENDOR) {
                console.log('🖥️ 拦截WebGL指纹检测');
                return 'Generic Renderer';
            }
            return originalGetParameter.apply(this, arguments);
        };

        console.log('🛡️ JavaScript检测绕过设置完成');
    }

    // 8. 直接注册API调用
    function setupDirectRegistration() {
        window.augmentDirectRegister = async function(email) {
            console.log('🚀 尝试直接注册:', email);

            const endpoints = [
                'https://login.augmentcode.com/dbconnections/signup',
                'https://auth.augmentcode.com/dbconnections/signup',
                'https://login.augmentcode.com/u/signup'
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Forwarded-For': CONFIG.fakeIP,
                            'CF-IPCountry': CONFIG.fakeCountry,
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'application/json',
                            'Origin': 'https://login.augmentcode.com',
                            'Referer': 'https://login.augmentcode.com/'
                        },
                        body: JSON.stringify({
                            email: email,
                            connection: 'Username-Password-Authentication',
                            client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
                        })
                    });

                    const result = await response.text();
                    console.log(`📡 ${endpoint} 响应:`, result);

                    if (response.ok) {
                        alert('✅ 注册成功！请检查邮箱验证邮件。');
                        return true;
                    }
                } catch (error) {
                    console.log(`❌ ${endpoint} 失败:`, error);
                }
            }

            alert('❌ 所有注册端点都失败了，请尝试VPN或联系官方。');
            return false;
        };

        console.log('📧 直接注册功能已设置，使用 augmentDirectRegister("email") 调用');
    }

    // 9. 主初始化函数
    function initialize() {
        console.log('🔧 初始化绕过器...');

        // 立即执行的伪装
        spoofGeolocation();
        spoofNavigatorProperties();
        interceptNetworkRequests();
        clearRegionCache();
        bypassJavaScriptDetection();
        setupDirectRegistration();

        // DOM加载后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                interceptDOMChanges();
                injectCSS();
            });
        } else {
            interceptDOMChanges();
            injectCSS();
        }

        // 定期清除缓存
        setInterval(clearRegionCache, 30000); // 每30秒清除一次

        console.log('✅ Augment Code 绕过器初始化完成');

        // 添加全局调试工具
        window.augmentBypass = {
            config: CONFIG,
            clearCache: clearRegionCache,
            spoofGeo: spoofGeolocation,
            directRegister: window.augmentDirectRegister,
            status: function() {
                console.log('🔍 绕过器状态:');
                console.log('- 地理位置伪装: ✅');
                console.log('- 网络请求拦截: ✅');
                console.log('- DOM监听: ✅');
                console.log('- 缓存清除: ✅');
                console.log('- JavaScript检测绕过: ✅');
                console.log('- 直接注册API: ✅');
            },
            help: function() {
                console.log('📖 使用帮助:');
                console.log('- augmentBypass.status() - 查看状态');
                console.log('- augmentBypass.clearCache() - 清除缓存');
                console.log('- augmentBypass.directRegister("email") - 直接注册');
                console.log('- augmentBypass.config - 查看配置');
            }
        };
    }
    
    // 10. 创建用户界面
    function createUI() {
        // 创建浮动控制面板
        const panel = document.createElement('div');
        panel.id = 'augment-bypass-panel';
        panel.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; z-index: 10000; background: #2d3748; color: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3); font-family: Arial, sans-serif; font-size: 12px; min-width: 250px;">
                <div style="font-weight: bold; margin-bottom: 10px; color: #4fd1c7;">🚀 Augment 绕过器</div>
                <div style="margin-bottom: 8px;">
                    <span style="color: #68d391;">●</span> 地理位置伪装: 已启用
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="color: #68d391;">●</span> 网络请求拦截: 已启用
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="color: #68d391;">●</span> 缓存清除: 已启用
                </div>
                <div style="margin-bottom: 10px;">
                    <input type="email" id="bypass-email" placeholder="输入邮箱地址" style="width: 100%; padding: 5px; border: 1px solid #4a5568; border-radius: 4px; background: #1a202c; color: white; margin-bottom: 5px;">
                    <button id="direct-register-btn" style="width: 100%; padding: 5px; background: #4299e1; color: white; border: none; border-radius: 4px; cursor: pointer;">直接注册</button>
                </div>
                <div style="font-size: 10px; color: #a0aec0; text-align: center;">
                    <button id="hide-panel-btn" style="background: none; border: none; color: #a0aec0; cursor: pointer; font-size: 10px;">隐藏面板</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // 绑定事件
        document.getElementById('direct-register-btn').addEventListener('click', function() {
            const email = document.getElementById('bypass-email').value;
            if (email) {
                window.augmentDirectRegister(email);
            } else {
                alert('请输入邮箱地址');
            }
        });

        document.getElementById('hide-panel-btn').addEventListener('click', function() {
            panel.style.display = 'none';

            // 创建显示按钮
            const showBtn = document.createElement('div');
            showBtn.innerHTML = '🚀';
            showBtn.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; background: #4299e1; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);';
            showBtn.addEventListener('click', function() {
                panel.style.display = 'block';
                showBtn.remove();
            });
            document.body.appendChild(showBtn);
        });

        console.log('🎛️ 用户界面已创建');
    }

    // 页面加载完成后的额外检查
    window.addEventListener('load', function() {
        console.log('📄 页面加载完成，执行最终检查...');

        // 创建用户界面
        setTimeout(createUI, 1000);

        // 检查是否有地区限制消息
        setTimeout(function() {
            const restrictionTexts = [
                'limiting signups in certain regions',
                'not available in your region',
                'region restriction',
                'geographical restriction'
            ];

            restrictionTexts.forEach(text => {
                const elements = document.querySelectorAll('*');
                elements.forEach(el => {
                    if (el.textContent && el.textContent.includes(text)) {
                        console.log('🚫 发现并隐藏地区限制消息:', el);
                        el.style.display = 'none';
                    }
                });
            });
        }, 2000);
    });

    // 启动绕过器
    initialize();

})();
