// ==UserScript==
// @name         Augment Code 地区限制绕过器 (简化版)
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  绕过 Augment Code 的地区注册限制 - 简化稳定版
// <AUTHOR>
// @match        https://www.augmentcode.com/*
// @match        https://login.augmentcode.com/*
// @match        https://auth.augmentcode.com/*
// @match        https://app.augmentcode.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 Augment Code 绕过器启动 (简化版)');
    
    // 配置
    const CONFIG = {
        fakeIP: '*******',
        fakeCountry: 'US',
        fakeLanguage: 'en-US'
    };
    
    // 1. 伪装地理位置
    function spoofGeolocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition = function(success, error) {
                console.log('🌍 伪装地理位置为美国');
                if (success) {
                    success({
                        coords: {
                            latitude: 40.7128,
                            longitude: -74.0060,
                            accuracy: 10
                        },
                        timestamp: Date.now()
                    });
                }
            };
        }
    }
    
    // 2. 伪装浏览器属性
    function spoofBrowser() {
        try {
            Object.defineProperty(navigator, 'language', {
                value: CONFIG.fakeLanguage,
                writable: false
            });
            
            Object.defineProperty(navigator, 'languages', {
                value: [CONFIG.fakeLanguage, 'en'],
                writable: false
            });
            
            console.log('🎭 浏览器属性伪装完成');
        } catch (e) {
            console.log('⚠️ 浏览器属性伪装失败:', e);
        }
    }
    
    // 3. 拦截网络请求
    function interceptRequests() {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const options = args[1] || {};
            
            if (!options.headers) {
                options.headers = {};
            }
            
            // 添加伪装头部
            options.headers['X-Forwarded-For'] = CONFIG.fakeIP;
            options.headers['CF-IPCountry'] = CONFIG.fakeCountry;
            options.headers['X-Real-IP'] = CONFIG.fakeIP;
            options.headers['Accept-Language'] = CONFIG.fakeLanguage + ',en;q=0.9';
            
            return originalFetch.apply(this, [args[0], options]);
        };
        
        console.log('📡 网络请求拦截设置完成');
    }
    
    // 4. 清除缓存
    function clearCache() {
        try {
            const keys = ['user_region', 'detected_country', 'geo_location', 'country_code'];
            keys.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });
            console.log('🗑️ 缓存清除完成');
        } catch (e) {
            console.log('⚠️ 缓存清除失败:', e);
        }
    }
    
    // 5. 隐藏地区限制消息
    function hideRegionBlocks() {
        const checkAndHide = () => {
            const elements = document.querySelectorAll('*');
            elements.forEach(el => {
                if (el.textContent && 
                    (el.textContent.includes('limiting signups in certain regions') ||
                     el.textContent.includes('not available in your region'))) {
                    el.style.display = 'none';
                    console.log('🚫 隐藏地区限制消息');
                }
            });
        };
        
        // 立即检查
        checkAndHide();
        
        // 定期检查
        setInterval(checkAndHide, 2000);
    }
    
    // 6. 直接注册功能
    function setupDirectRegister() {
        window.augmentDirectRegister = async function(email) {
            console.log('📧 尝试直接注册:', email);
            
            try {
                const response = await fetch('https://login.augmentcode.com/dbconnections/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Forwarded-For': CONFIG.fakeIP,
                        'CF-IPCountry': CONFIG.fakeCountry,
                        'Origin': 'https://login.augmentcode.com'
                    },
                    body: JSON.stringify({
                        email: email,
                        connection: 'Username-Password-Authentication',
                        client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
                    })
                });
                
                const result = await response.text();
                console.log('📡 注册响应:', result);
                
                if (response.ok) {
                    alert('✅ 注册成功！请检查邮箱。');
                    return true;
                } else {
                    alert('❌ 注册失败: ' + result);
                    return false;
                }
            } catch (error) {
                console.log('❌ 注册错误:', error);
                alert('❌ 注册失败: ' + error.message);
                return false;
            }
        };
    }
    
    // 7. 创建简单UI
    function createUI() {
        const panel = document.createElement('div');
        panel.innerHTML = `
            <div id="augment-panel" style="position: fixed; top: 20px; right: 20px; z-index: 10000; background: #333; color: white; padding: 15px; border-radius: 8px; font-family: Arial, sans-serif; font-size: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.5);">
                <div style="font-weight: bold; margin-bottom: 10px;">🚀 Augment 绕过器</div>
                <div style="margin-bottom: 10px; color: #4CAF50;">● 地区伪装已启用</div>
                <input type="email" id="bypass-email" placeholder="输入邮箱" style="width: 200px; padding: 5px; margin-bottom: 5px; border: 1px solid #555; background: #222; color: white; border-radius: 3px;">
                <br>
                <button id="register-btn" style="padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer;">直接注册</button>
                <button id="hide-btn" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer; margin-left: 5px;">隐藏</button>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定事件
        document.getElementById('register-btn').onclick = function() {
            const email = document.getElementById('bypass-email').value;
            if (email) {
                window.augmentDirectRegister(email);
            } else {
                alert('请输入邮箱地址');
            }
        };
        
        document.getElementById('hide-btn').onclick = function() {
            document.getElementById('augment-panel').style.display = 'none';
        };
        
        console.log('🎛️ UI创建完成');
    }
    
    // 8. 初始化
    function init() {
        console.log('🔧 初始化绕过器...');
        
        spoofGeolocation();
        spoofBrowser();
        interceptRequests();
        clearCache();
        setupDirectRegister();
        
        // DOM加载后创建UI
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(createUI, 1000);
                hideRegionBlocks();
            });
        } else {
            setTimeout(createUI, 1000);
            hideRegionBlocks();
        }
        
        // 定期清除缓存
        setInterval(clearCache, 30000);
        
        console.log('✅ 绕过器初始化完成');
        
        // 全局调试工具
        window.augmentBypass = {
            clearCache: clearCache,
            directRegister: window.augmentDirectRegister,
            status: () => console.log('✅ 绕过器运行中')
        };
    }
    
    // 启动
    init();
    
})();
