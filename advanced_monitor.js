// Augment Code API 监控脚本
// 在浏览器控制台中运行此脚本

(function() {
    'use strict';
    
    console.log('🔍 Augment Code API 监控器启动中...');
    
    // 存储捕获的请求
    window.augmentRequests = [];
    
    // 创建监控面板
    function createMonitorPanel() {
        const panel = document.createElement('div');
        panel.id = 'augment-monitor-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 500px;
            background: white;
            border: 2px solid #007cba;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 12px;
            overflow: hidden;
        `;
        
        panel.innerHTML = `
            <div style="background: #007cba; color: white; padding: 10px; font-weight: bold;">
                🔍 Augment API 监控器
                <button onclick="document.getElementById('augment-monitor-panel').remove()" 
                        style="float: right; background: none; border: none; color: white; cursor: pointer;">✕</button>
            </div>
            <div id="monitor-content" style="padding: 10px; max-height: 400px; overflow-y: auto;">
                <div style="margin-bottom: 10px;">
                    <button onclick="window.clearAugmentRequests()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">清除</button>
                    <button onclick="window.exportAugmentRequests()" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">导出</button>
                </div>
                <div id="requests-list">等待网络请求...</div>
            </div>
        `;
        
        document.body.appendChild(panel);
        return panel;
    }
    
    // 显示请求
    function displayRequest(request) {
        const requestsList = document.getElementById('requests-list');
        if (!requestsList) return;
        
        const requestDiv = document.createElement('div');
        requestDiv.style.cssText = `
            border: 1px solid #ddd;
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            background: #f8f9fa;
        `;
        
        const isAugmentRelated = request.url.includes('augmentcode.com') || 
                                request.url.includes('auth.augmentcode.com');
        
        if (isAugmentRelated) {
            requestDiv.style.background = '#fff3cd';
            requestDiv.style.borderColor = '#ffc107';
        }
        
        requestDiv.innerHTML = `
            <div style="font-weight: bold; color: ${isAugmentRelated ? '#856404' : '#333'};">
                <span style="background: ${getMethodColor(request.method)}; color: white; padding: 2px 6px; border-radius: 2px; font-size: 10px;">${request.method}</span>
                ${request.url.length > 50 ? request.url.substring(0, 50) + '...' : request.url}
            </div>
            <div style="margin-top: 5px; font-size: 10px; color: #666;">
                ${new Date(request.timestamp).toLocaleTimeString()}
                <button onclick="window.showRequestDetails('${request.id}')" 
                        style="float: right; background: #007cba; color: white; border: none; padding: 2px 6px; border-radius: 2px; cursor: pointer; font-size: 10px;">详情</button>
            </div>
        `;
        
        requestsList.insertBefore(requestDiv, requestsList.firstChild);
    }
    
    function getMethodColor(method) {
        const colors = {
            'GET': '#28a745',
            'POST': '#007bff',
            'PUT': '#ffc107',
            'DELETE': '#dc3545',
            'PATCH': '#6f42c1'
        };
        return colors[method] || '#6c757d';
    }
    
    // 拦截 fetch 请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = typeof args[0] === 'string' ? args[0] : args[0].url;
        const options = args[1] || {};
        
        const request = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            type: 'FETCH',
            url: url,
            method: options.method || 'GET',
            headers: options.headers || {},
            body: options.body || null
        };
        
        window.augmentRequests.push(request);
        displayRequest(request);
        
        console.log('📡 FETCH请求:', request);
        
        return originalFetch.apply(this, args).then(response => {
            console.log('📡 FETCH响应:', {
                url: url,
                status: response.status,
                statusText: response.statusText
            });
            return response;
        });
    };
    
    // 拦截 XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._augment_method = method;
        this._augment_url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(data) {
        const request = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            type: 'XHR',
            url: this._augment_url,
            method: this._augment_method || 'GET',
            headers: {},
            body: data || null
        };
        
        window.augmentRequests.push(request);
        displayRequest(request);
        
        console.log('📡 XHR请求:', request);
        
        return originalXHRSend.apply(this, [data]);
    };
    
    // 全局函数
    window.clearAugmentRequests = function() {
        window.augmentRequests = [];
        const requestsList = document.getElementById('requests-list');
        if (requestsList) {
            requestsList.innerHTML = '等待网络请求...';
        }
        console.log('🗑️ 已清除所有请求记录');
    };
    
    window.exportAugmentRequests = function() {
        const data = JSON.stringify(window.augmentRequests, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `augment_requests_${new Date().getTime()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        console.log('💾 请求数据已导出');
    };
    
    window.showRequestDetails = function(requestId) {
        const request = window.augmentRequests.find(r => r.id == requestId);
        if (!request) return;
        
        const details = `
请求详情:
========
URL: ${request.url}
方法: ${request.method}
类型: ${request.type}
时间: ${request.timestamp}

请求头:
${JSON.stringify(request.headers, null, 2)}

请求体:
${request.body || '无'}

cURL命令:
curl -X ${request.method} '${request.url}'${request.headers ? Object.entries(request.headers).map(([k,v]) => ` -H '${k}: ${v}'`).join('') : ''}${request.body ? ` -d '${request.body}'` : ''}
        `;
        
        console.log(details);
        
        // 复制cURL到剪贴板
        const curl = `curl -X ${request.method} '${request.url}'${request.headers ? Object.entries(request.headers).map(([k,v]) => ` -H '${k}: ${v}'`).join('') : ''}${request.body ? ` -d '${request.body}'` : ''}`;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(curl).then(() => {
                alert('cURL命令已复制到剪贴板！\n\n详细信息请查看控制台。');
            });
        } else {
            alert('详细信息已输出到控制台');
        }
    };
    
    // 创建监控面板
    createMonitorPanel();
    
    console.log('✅ Augment Code API 监控器已启动！');
    console.log('📋 使用说明:');
    console.log('1. 监控面板已显示在页面右上角');
    console.log('2. 所有网络请求将被自动捕获');
    console.log('3. Augment相关请求会高亮显示');
    console.log('4. 点击"详情"按钮查看完整请求信息');
    console.log('5. 使用 window.augmentRequests 访问所有捕获的请求');
    
})();
