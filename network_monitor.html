<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Code Network Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.monitoring {
            background: #d4edda;
            color: #155724;
        }
        .status.stopped {
            background: #f8d7da;
            color: #721c24;
        }
        .request-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #fafafa;
        }
        .request-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            margin-right: 10px;
        }
        .method.GET { background: #28a745; }
        .method.POST { background: #007bff; }
        .method.PUT { background: #ffc107; color: #000; }
        .method.DELETE { background: #dc3545; }
        .details {
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .copy-btn {
            background: #28a745;
            font-size: 12px;
            padding: 5px 10px;
        }
        #iframe-container {
            margin-top: 20px;
            border: 2px solid #007cba;
            border-radius: 5px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Augment Code 网络请求监控器</h1>
        
        <div class="controls">
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()">停止监控</button>
            <button onclick="clearRequests()">清除记录</button>
            <button onclick="exportRequests()">导出数据</button>
            <div id="status" class="status stopped">状态: 未开始监控</div>
        </div>

        <div id="iframe-container">
            <iframe id="augment-frame" src="about:blank"></iframe>
        </div>

        <h2>📡 捕获的网络请求</h2>
        <div id="requests-container"></div>
    </div>

    <script>
        let isMonitoring = false;
        let capturedRequests = [];
        let originalFetch = window.fetch;
        let originalXHR = window.XMLHttpRequest;

        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            document.getElementById('status').textContent = '状态: 正在监控...';
            document.getElementById('status').className = 'status monitoring';
            
            // 加载Augment Code页面
            document.getElementById('augment-frame').src = 'https://www.augmentcode.com/install';
            
            // 监控fetch请求
            window.fetch = function(...args) {
                captureRequest('FETCH', args[0], args[1]);
                return originalFetch.apply(this, args);
            };

            // 监控XMLHttpRequest
            const originalOpen = XMLHttpRequest.prototype.open;
            const originalSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._method = method;
                this._url = url;
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                captureRequest('XHR', this._url, {
                    method: this._method,
                    body: data
                });
                return originalSend.apply(this, [data]);
            };

            console.log('🔍 网络监控已启动');
        }

        function stopMonitoring() {
            isMonitoring = false;
            document.getElementById('status').textContent = '状态: 监控已停止';
            document.getElementById('status').className = 'status stopped';
            
            // 恢复原始函数
            window.fetch = originalFetch;
            
            console.log('⏹️ 网络监控已停止');
        }

        function captureRequest(type, url, options = {}) {
            if (!isMonitoring) return;
            
            const request = {
                timestamp: new Date().toISOString(),
                type: type,
                url: url,
                method: options.method || 'GET',
                headers: options.headers || {},
                body: options.body || null,
                id: Date.now() + Math.random()
            };
            
            capturedRequests.push(request);
            displayRequest(request);
            
            console.log('📡 捕获请求:', request);
        }

        function displayRequest(request) {
            const container = document.getElementById('requests-container');
            const requestDiv = document.createElement('div');
            requestDiv.className = 'request-item';
            requestDiv.innerHTML = `
                <div class="request-header">
                    <span class="method ${request.method}">${request.method}</span>
                    <span>${request.url}</span>
                    <span style="float: right; color: #666;">${new Date(request.timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="details" id="details-${request.id}">
URL: ${request.url}
Method: ${request.method}
Type: ${request.type}
Headers: ${JSON.stringify(request.headers, null, 2)}
Body: ${request.body || 'null'}
                </div>
                <button class="copy-btn" onclick="copyRequest('${request.id}')">复制为cURL</button>
            `;
            
            container.insertBefore(requestDiv, container.firstChild);
        }

        function copyRequest(requestId) {
            const request = capturedRequests.find(r => r.id == requestId);
            if (!request) return;
            
            let curl = `curl -X ${request.method} '${request.url}'`;
            
            if (request.headers) {
                Object.entries(request.headers).forEach(([key, value]) => {
                    curl += ` -H '${key}: ${value}'`;
                });
            }
            
            if (request.body) {
                curl += ` -d '${request.body}'`;
            }
            
            navigator.clipboard.writeText(curl).then(() => {
                alert('cURL命令已复制到剪贴板！');
            });
        }

        function clearRequests() {
            capturedRequests = [];
            document.getElementById('requests-container').innerHTML = '';
        }

        function exportRequests() {
            const data = JSON.stringify(capturedRequests, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'augment_requests.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('🚀 Augment Code 网络监控器已就绪');
            console.log('📋 使用说明:');
            console.log('1. 点击"开始监控"按钮');
            console.log('2. 在下方的iframe中进行注册操作');
            console.log('3. 所有网络请求将被自动捕获和显示');
            console.log('4. 点击"复制为cURL"可以获取API调用命令');
        };
    </script>
</body>
</html>
