// Augment Code 注册绕过脚本
// 在浏览器控制台中运行

(function() {
    'use strict';
    
    console.log('🚀 启动 Augment Code 注册绕过脚本...');
    
    // 1. 伪装地理位置和浏览器信息
    function spoofGeoLocation() {
        // 禁用地理位置检测
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition = function(success, error) {
                // 伪装美国位置 (纽约)
                success({
                    coords: {
                        latitude: 40.7128,
                        longitude: -74.0060,
                        accuracy: 10
                    },
                    timestamp: Date.now()
                });
            };
        }
        
        // 伪装语言
        Object.defineProperty(navigator, 'language', {
            value: 'en-US',
            writable: false
        });
        
        Object.defineProperty(navigator, 'languages', {
            value: ['en-US', 'en'],
            writable: false
        });
        
        console.log('✅ 地理位置伪装完成');
    }
    
    // 2. 清除可能的地区检测缓存
    function clearRegionCache() {
        try {
            localStorage.removeItem('user_region');
            localStorage.removeItem('detected_country');
            localStorage.removeItem('geo_location');
            sessionStorage.clear();
            console.log('✅ 地区缓存清除完成');
        } catch (e) {
            console.log('⚠️ 缓存清除失败:', e);
        }
    }
    
    // 3. 拦截并修改网络请求
    function interceptRequests() {
        const originalFetch = window.fetch;
        
        window.fetch = function(...args) {
            const url = typeof args[0] === 'string' ? args[0] : args[0].url;
            const options = args[1] || {};
            
            // 修改请求头，伪装来源
            if (!options.headers) {
                options.headers = {};
            }
            
            // 添加美国IP伪装头
            options.headers['X-Forwarded-For'] = '*******';
            options.headers['CF-IPCountry'] = 'US';
            options.headers['X-Real-IP'] = '*******';
            options.headers['X-Country-Code'] = 'US';
            
            console.log('🔄 拦截请求:', url);
            
            return originalFetch.apply(this, [args[0], options]);
        };
        
        console.log('✅ 网络请求拦截设置完成');
    }
    
    // 4. 直接调用注册API
    async function directRegistration(email) {
        const registrationEndpoints = [
            'https://login.augmentcode.com/dbconnections/signup',
            'https://auth.augmentcode.com/dbconnections/signup',
            'https://api.augmentcode.com/auth/register',
            'https://login.augmentcode.com/u/signup'
        ];
        
        for (const endpoint of registrationEndpoints) {
            try {
                console.log(`🔄 尝试注册端点: ${endpoint}`);
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Forwarded-For': '*******',
                        'CF-IPCountry': 'US',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json',
                        'Origin': 'https://login.augmentcode.com',
                        'Referer': 'https://login.augmentcode.com/'
                    },
                    body: JSON.stringify({
                        email: email,
                        connection: 'Username-Password-Authentication',
                        client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
                    })
                });
                
                const result = await response.text();
                console.log(`📡 响应 (${response.status}):`, result);
                
                if (response.ok) {
                    console.log('✅ 注册成功!');
                    return true;
                }
                
            } catch (error) {
                console.log(`❌ 端点 ${endpoint} 失败:`, error);
            }
        }
        
        return false;
    }
    
    // 5. 主函数
    async function main() {
        spoofGeoLocation();
        clearRegionCache();
        interceptRequests();
        
        // 等待用户输入邮箱
        const email = prompt('请输入你的邮箱地址:');
        if (email) {
            console.log(`🔄 尝试为 ${email} 注册...`);
            const success = await directRegistration(email);
            
            if (success) {
                alert('注册成功！请检查邮箱验证邮件。');
            } else {
                alert('直接注册失败，请尝试其他方法。');
                console.log('💡 建议:');
                console.log('1. 使用VPN连接美国服务器');
                console.log('2. 清除所有浏览器数据');
                console.log('3. 使用无痕模式重试');
            }
        }
    }
    
    // 6. 提供手动工具
    window.augmentBypass = {
        spoofGeo: spoofGeoLocation,
        clearCache: clearRegionCache,
        register: directRegistration,
        run: main
    };
    
    console.log('✅ 绕过脚本加载完成!');
    console.log('💡 使用方法:');
    console.log('1. 运行 augmentBypass.run() 开始自动绕过');
    console.log('2. 或者手动调用 augmentBypass.register("<EMAIL>")');
    
    // 自动运行
    main();
    
})();
