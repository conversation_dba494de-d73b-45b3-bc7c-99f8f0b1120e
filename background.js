// 后台脚本
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'directRegister') {
        console.log('📧 后台处理注册请求:', request.email);
        
        fetch('https://login.augmentcode.com/dbconnections/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Forwarded-For': '*******',
                'CF-IPCountry': 'US',
                'Origin': 'https://login.augmentcode.com'
            },
            body: JSON.stringify({
                email: request.email,
                connection: 'Username-Password-Authentication',
                client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
            })
        })
        .then(response => {
            console.log('📡 注册响应:', response);
            if (response.ok) {
                chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'showResult',
                        success: true,
                        message: '注册成功！请检查邮箱。'
                    });
                });
            } else {
                chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'showResult',
                        success: false,
                        message: '注册失败'
                    });
                });
            }
        })
        .catch(error => {
            console.log('❌ 注册错误:', error);
            chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'showResult',
                    success: false,
                    message: '请求失败: ' + error.message
                });
            });
        });
    }
});

// 修改请求头
chrome.webRequest.onBeforeSendHeaders.addListener(
    (details) => {
        const headers = details.requestHeaders || [];
        
        // 添加伪装头部
        headers.push({name: 'X-Forwarded-For', value: '*******'});
        headers.push({name: 'CF-IPCountry', value: 'US'});
        headers.push({name: 'Accept-Language', value: 'en-US,en;q=0.9'});
        
        return {requestHeaders: headers};
    },
    {
        urls: [
            "https://www.augmentcode.com/*",
            "https://login.augmentcode.com/*",
            "https://auth.augmentcode.com/*"
        ]
    },
    ["blocking", "requestHeaders"]
);
