// Augment Code 绕过内容脚本
(function() {
    'use strict';
    
    console.log('🚀 Augment 扩展绕过器启动');
    
    // 配置
    const CONFIG = {
        fakeIP: '*******',
        fakeCountry: 'US',
        fakeLanguage: 'en-US',
        fakeCoords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10 }
    };
    
    // 1. 伪装地理位置
    if (navigator.geolocation) {
        const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
        const originalWatchPosition = navigator.geolocation.watchPosition;
        
        navigator.geolocation.getCurrentPosition = function(success, error, options) {
            console.log('🌍 拦截地理位置请求');
            if (success) {
                success({
                    coords: CONFIG.fakeCoords,
                    timestamp: Date.now()
                });
            }
        };
        
        navigator.geolocation.watchPosition = function(success, error, options) {
            console.log('🌍 拦截地理位置监听');
            if (success) {
                success({
                    coords: CONFIG.fakeCoords,
                    timestamp: Date.now()
                });
            }
            return 1;
        };
    }
    
    // 2. 伪装语言
    Object.defineProperty(navigator, 'language', {
        value: CONFIG.fakeLanguage,
        writable: false
    });
    
    Object.defineProperty(navigator, 'languages', {
        value: [CONFIG.fakeLanguage, 'en'],
        writable: false
    });
    
    // 3. 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const options = args[1] || {};
        if (!options.headers) options.headers = {};
        
        options.headers['X-Forwarded-For'] = CONFIG.fakeIP;
        options.headers['CF-IPCountry'] = CONFIG.fakeCountry;
        options.headers['Accept-Language'] = CONFIG.fakeLanguage + ',en;q=0.9';
        
        console.log('📡 拦截fetch:', args[0]);
        return originalFetch.apply(this, [args[0], options]);
    };
    
    // 4. 清除缓存
    function clearCache() {
        const keys = ['user_region', 'detected_country', 'geo_location'];
        keys.forEach(key => {
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
    }
    clearCache();
    setInterval(clearCache, 30000);
    
    // 5. 隐藏地区限制消息
    function hideRegionMessages() {
        const elements = document.querySelectorAll('*');
        elements.forEach(el => {
            if (el.textContent && el.textContent.includes('limiting signups in certain regions')) {
                el.style.display = 'none';
                console.log('🚫 隐藏地区限制消息');
            }
        });
    }
    
    // 6. 创建注册按钮
    function createButton() {
        if (document.getElementById('augment-bypass-btn')) return;
        
        const button = document.createElement('div');
        button.id = 'augment-bypass-btn';
        button.innerHTML = '🚀 直接注册';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        
        button.onclick = () => {
            const email = prompt('请输入邮箱地址:');
            if (email) {
                // 发送消息给background script
                chrome.runtime.sendMessage({
                    action: 'directRegister',
                    email: email
                });
            }
        };
        
        document.body.appendChild(button);
    }
    
    // 7. 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(createButton, 1000);
            setInterval(hideRegionMessages, 3000);
        });
    } else {
        setTimeout(createButton, 1000);
        setInterval(hideRegionMessages, 3000);
    }
    
    console.log('✅ 扩展绕过器初始化完成');
    
})();
