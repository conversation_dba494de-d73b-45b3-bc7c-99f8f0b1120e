#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 自动注册脚本
使用方法: python augment_register.py <EMAIL>
"""

import requests
import json
import sys
import time
from fake_useragent import UserAgent

def register_augment(email):
    """
    直接调用Augment Code的注册API
    """
    print(f"🚀 开始注册 Augment Code 账户: {email}")
    
    # 伪装美国用户
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/',
        'X-Forwarded-For': '*******',  # Google DNS (美国)
        'CF-IPCountry': 'US',
        'X-Real-IP': '*******',
        'X-Country-Code': 'US',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
    }
    
    # 注册数据
    data = {
        'email': email,
        'connection': 'Username-Password-Authentication',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
    }
    
    # 尝试多个可能的端点
    endpoints = [
        'https://login.augmentcode.com/dbconnections/signup',
        'https://auth.augmentcode.com/dbconnections/signup',
        'https://login.augmentcode.com/u/signup',
        'https://api.augmentcode.com/auth/register'
    ]
    
    session = requests.Session()
    session.headers.update(headers)
    
    for endpoint in endpoints:
        try:
            print(f"📡 尝试端点: {endpoint}")
            
            response = session.post(
                endpoint,
                json=data,
                timeout=30,
                allow_redirects=True
            )
            
            print(f"📊 响应状态: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("✅ 注册成功！请检查邮箱验证邮件。")
                return True
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    if 'email' in error_data.get('description', '').lower():
                        print("⚠️ 邮箱可能已被注册")
                    else:
                        print(f"❌ 请求错误: {error_data}")
                except:
                    print(f"❌ 请求错误: {response.text}")
            elif response.status_code == 429:
                print("⏳ 请求过于频繁，等待30秒后重试...")
                time.sleep(30)
                continue
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
            continue
        except Exception as e:
            print(f"❌ 未知错误: {e}")
            continue
    
    print("❌ 所有端点都失败了")
    return False

def main():
    if len(sys.argv) != 2:
        print("使用方法: python augment_register.py <EMAIL>")
        sys.exit(1)
    
    email = sys.argv[1]
    
    if '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        sys.exit(1)
    
    success = register_augment(email)
    
    if success:
        print("\n🎉 注册流程完成！")
        print("📧 请检查你的邮箱，点击验证链接完成注册。")
    else:
        print("\n💡 建议:")
        print("1. 使用VPN连接美国服务器")
        print("2. 尝试不同的邮箱地址")
        print("3. 稍后再试")

if __name__ == "__main__":
    main()
