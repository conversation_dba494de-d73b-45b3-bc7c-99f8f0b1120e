<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .status {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .status-item {
            margin: 5px 0;
            font-size: 12px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 3px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            width: 100%;
            padding: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn-secondary {
            background: #2196F3;
        }
        .btn-secondary:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 10px;
            padding: 8px;
            border-radius: 3px;
            font-size: 12px;
            text-align: center;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
        }
    </style>
</head>
<body>
    <div class="title">🚀 Augment 绕过器</div>
    
    <div class="status">
        <div class="status-item">🌍 地理位置伪装: <span style="color: #4CAF50;">已启用</span></div>
        <div class="status-item">🎭 语言伪装: <span style="color: #4CAF50;">已启用</span></div>
        <div class="status-item">📡 请求拦截: <span style="color: #4CAF50;">已启用</span></div>
    </div>
    
    <div class="input-group">
        <label for="email">邮箱地址:</label>
        <input type="email" id="email" placeholder="输入你的邮箱地址">
    </div>
    
    <button class="btn" id="registerBtn">直接注册</button>
    <button class="btn btn-secondary" id="clearCacheBtn">清除缓存</button>
    
    <div id="result"></div>
    
    <script src="popup.js"></script>
</body>
</html>
