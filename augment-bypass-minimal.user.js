// ==UserScript==
// @name         Augment Code 绕过器 (最小版)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  最小化的 Augment Code 地区限制绕过器
// <AUTHOR>
// @match        https://www.augmentcode.com/*
// @match        https://login.augmentcode.com/*
// @match        https://auth.augmentcode.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 Augment 最小绕过器启动');
    
    // 1. 伪装地理位置
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition = function(success) {
            console.log('🌍 伪装GPS位置');
            if (success) {
                success({
                    coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10 },
                    timestamp: Date.now()
                });
            }
        };
    }
    
    // 2. 伪装语言
    try {
        Object.defineProperty(navigator, 'language', { value: 'en-US', writable: false });
        Object.defineProperty(navigator, 'languages', { value: ['en-US', 'en'], writable: false });
        console.log('🎭 语言伪装完成');
    } catch (e) {
        console.log('语言伪装失败:', e);
    }
    
    // 3. 拦截网络请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const options = args[1] || {};
        if (!options.headers) options.headers = {};
        
        // 添加美国IP头部
        options.headers['X-Forwarded-For'] = '*******';
        options.headers['CF-IPCountry'] = 'US';
        options.headers['Accept-Language'] = 'en-US,en;q=0.9';
        
        console.log('📡 拦截请求:', args[0]);
        return originalFetch.apply(this, [args[0], options]);
    };
    
    // 4. 清除地区缓存
    function clearCache() {
        try {
            localStorage.removeItem('user_region');
            localStorage.removeItem('detected_country');
            sessionStorage.clear();
            console.log('🗑️ 缓存已清除');
        } catch (e) {
            console.log('缓存清除失败:', e);
        }
    }
    clearCache();
    setInterval(clearCache, 30000);
    
    // 5. 隐藏地区限制消息
    function hideBlocks() {
        const elements = document.querySelectorAll('*');
        elements.forEach(el => {
            if (el.textContent && el.textContent.includes('limiting signups in certain regions')) {
                el.style.display = 'none';
                console.log('🚫 隐藏限制消息');
            }
        });
    }
    
    // 6. 直接注册功能
    window.directRegister = async function(email) {
        console.log('📧 直接注册:', email);
        try {
            const response = await fetch('https://login.augmentcode.com/dbconnections/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Forwarded-For': '*******',
                    'CF-IPCountry': 'US'
                },
                body: JSON.stringify({
                    email: email,
                    connection: 'Username-Password-Authentication',
                    client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
                })
            });
            
            if (response.ok) {
                alert('✅ 注册成功！');
                return true;
            } else {
                alert('❌ 注册失败');
                return false;
            }
        } catch (error) {
            alert('❌ 错误: ' + error.message);
            return false;
        }
    };
    
    // 7. 页面加载后处理
    window.addEventListener('load', function() {
        console.log('📄 页面加载完成');
        
        // 隐藏限制消息
        setTimeout(hideBlocks, 1000);
        setInterval(hideBlocks, 5000);
        
        // 创建简单按钮
        setTimeout(function() {
            const btn = document.createElement('div');
            btn.innerHTML = '🚀';
            btn.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; background: #4CAF50; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.3);';
            btn.title = '点击直接注册';
            
            btn.onclick = function() {
                const email = prompt('输入邮箱地址:');
                if (email) {
                    window.directRegister(email);
                }
            };
            
            document.body.appendChild(btn);
            console.log('🎛️ 按钮已创建');
        }, 2000);
    });
    
    console.log('✅ 最小绕过器初始化完成');
    console.log('💡 使用 directRegister("email") 直接注册');
    
})();
