// ==UserScript==
// @name         Augment Code 绕过器 (油猴专用版)
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  专为Tampermonkey优化的Augment Code绕过器
// <AUTHOR>
// @match        https://www.augmentcode.com/*
// @match        https://login.augmentcode.com/*
// @match        https://auth.augmentcode.com/*
// @match        https://app.augmentcode.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-start
// @updateURL    none
// @downloadURL  none
// ==/UserScript==

(function() {
    'use strict';
    
    // 防止重复执行
    if (window.augmentBypassLoaded) return;
    window.augmentBypassLoaded = true;
    
    console.log('🚀 Augment 油猴专用绕过器启动');
    
    // 配置
    const CONFIG = {
        fakeIP: '*******',
        fakeCountry: 'US',
        fakeLanguage: 'en-US',
        fakeCoords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10 }
    };
    
    // 1. 早期拦截 - 在页面脚本执行前
    function earlyIntercept() {
        // 伪装地理位置API
        const originalGeolocation = navigator.geolocation;
        if (originalGeolocation) {
            Object.defineProperty(navigator, 'geolocation', {
                value: {
                    getCurrentPosition: function(success, error, options) {
                        console.log('🌍 拦截地理位置请求');
                        if (success) {
                            setTimeout(() => {
                                success({
                                    coords: CONFIG.fakeCoords,
                                    timestamp: Date.now()
                                });
                            }, 100);
                        }
                    },
                    watchPosition: function(success, error, options) {
                        console.log('🌍 拦截地理位置监听');
                        if (success) {
                            setTimeout(() => {
                                success({
                                    coords: CONFIG.fakeCoords,
                                    timestamp: Date.now()
                                });
                            }, 100);
                        }
                        return 1;
                    },
                    clearWatch: function() {}
                },
                configurable: false,
                writable: false
            });
        }
        
        // 伪装语言
        try {
            Object.defineProperty(navigator, 'language', {
                value: CONFIG.fakeLanguage,
                configurable: false,
                writable: false
            });
            
            Object.defineProperty(navigator, 'languages', {
                value: [CONFIG.fakeLanguage, 'en'],
                configurable: false,
                writable: false
            });
            
            console.log('🎭 语言伪装完成');
        } catch (e) {
            console.log('⚠️ 语言伪装失败:', e);
        }
        
        // 伪装时区
        try {
            const originalDateTimeFormat = Intl.DateTimeFormat;
            Intl.DateTimeFormat = function(...args) {
                const formatter = new originalDateTimeFormat(...args);
                const originalResolvedOptions = formatter.resolvedOptions;
                formatter.resolvedOptions = function() {
                    const options = originalResolvedOptions.call(this);
                    options.timeZone = 'America/New_York';
                    return options;
                };
                return formatter;
            };
            
            // 伪装Date对象的时区方法
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return 300; // UTC-5 (纽约时区)
            };
            
            console.log('🕐 时区伪装完成');
        } catch (e) {
            console.log('⚠️ 时区伪装失败:', e);
        }
    }
    
    // 2. 网络请求拦截
    function interceptNetworkRequests() {
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = typeof args[0] === 'string' ? args[0] : args[0].url;
            const options = args[1] || {};
            
            if (!options.headers) {
                options.headers = {};
            }
            
            // 添加伪装头部
            options.headers['X-Forwarded-For'] = CONFIG.fakeIP;
            options.headers['CF-IPCountry'] = CONFIG.fakeCountry;
            options.headers['X-Real-IP'] = CONFIG.fakeIP;
            options.headers['Accept-Language'] = CONFIG.fakeLanguage + ',en;q=0.9';
            
            console.log('📡 拦截fetch请求:', url);
            return originalFetch.apply(this, [args[0], options]);
        };
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._augment_method = method;
            this._augment_url = url;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            // 添加伪装头部
            try {
                this.setRequestHeader('X-Forwarded-For', CONFIG.fakeIP);
                this.setRequestHeader('CF-IPCountry', CONFIG.fakeCountry);
                this.setRequestHeader('X-Real-IP', CONFIG.fakeIP);
                this.setRequestHeader('Accept-Language', CONFIG.fakeLanguage + ',en;q=0.9');
            } catch (e) {
                console.log('⚠️ XHR头部设置失败:', e);
            }
            
            console.log('📡 拦截XHR请求:', this._augment_url);
            return originalXHRSend.apply(this, [data]);
        };
        
        console.log('🔄 网络请求拦截设置完成');
    }
    
    // 3. 清除缓存
    function clearRegionCache() {
        try {
            const keysToRemove = [
                'user_region', 'detected_country', 'geo_location', 
                'country_code', 'region_blocked', 'augment_region'
            ];
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
                // 使用GM API存储
                GM_setValue(key, null);
            });
            
            console.log('🗑️ 缓存清除完成');
        } catch (e) {
            console.log('⚠️ 缓存清除失败:', e);
        }
    }
    
    // 4. 使用GM_xmlhttpRequest的直接注册
    function setupDirectRegister() {
        window.augmentDirectRegister = function(email) {
            console.log('📧 使用GM API直接注册:', email);
            
            const requestData = {
                email: email,
                connection: 'Username-Password-Authentication',
                client_id: 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
            };
            
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://login.augmentcode.com/dbconnections/signup',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Forwarded-For': CONFIG.fakeIP,
                    'CF-IPCountry': CONFIG.fakeCountry,
                    'X-Real-IP': CONFIG.fakeIP,
                    'Accept-Language': CONFIG.fakeLanguage + ',en;q=0.9',
                    'Origin': 'https://login.augmentcode.com',
                    'Referer': 'https://login.augmentcode.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                },
                data: JSON.stringify(requestData),
                onload: function(response) {
                    console.log('📡 GM注册响应:', response);
                    if (response.status >= 200 && response.status < 300) {
                        alert('✅ 注册成功！请检查邮箱验证邮件。');
                    } else {
                        alert('❌ 注册失败: ' + response.responseText);
                    }
                },
                onerror: function(error) {
                    console.log('❌ GM注册错误:', error);
                    alert('❌ 注册请求失败: ' + error.error);
                }
            });
        };
        
        console.log('📧 GM直接注册功能已设置');
    }
    
    // 5. 创建UI
    function createUI() {
        // 等待body存在
        const waitForBody = setInterval(() => {
            if (document.body) {
                clearInterval(waitForBody);
                
                const button = document.createElement('div');
                button.innerHTML = '🚀';
                button.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 999999;
                    background: linear-gradient(45deg, #4CAF50, #45a049);
                    color: white;
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    font-size: 24px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                    transition: all 0.3s ease;
                    font-family: Arial, sans-serif;
                `;
                
                button.title = 'Augment 绕过器 - 点击直接注册';
                
                // 悬停效果
                button.onmouseenter = () => {
                    button.style.transform = 'scale(1.1)';
                    button.style.boxShadow = '0 6px 20px rgba(0,0,0,0.4)';
                };
                
                button.onmouseleave = () => {
                    button.style.transform = 'scale(1)';
                    button.style.boxShadow = '0 4px 15px rgba(0,0,0,0.3)';
                };
                
                button.onclick = () => {
                    const email = prompt('🚀 Augment Code 绕过器\n\n请输入你的邮箱地址:');
                    if (email && email.includes('@')) {
                        window.augmentDirectRegister(email);
                    } else if (email) {
                        alert('❌ 请输入有效的邮箱地址');
                    }
                };
                
                document.body.appendChild(button);
                console.log('🎛️ UI按钮已创建');
            }
        }, 100);
        
        // 10秒后如果还没找到body就放弃
        setTimeout(() => clearInterval(waitForBody), 10000);
    }
    
    // 6. 隐藏地区限制消息
    function hideRegionMessages() {
        const hideMessages = () => {
            const restrictionTexts = [
                'limiting signups in certain regions',
                'not available in your region',
                'region restriction',
                'geographical restriction'
            ];
            
            document.querySelectorAll('*').forEach(el => {
                if (el.textContent) {
                    restrictionTexts.forEach(text => {
                        if (el.textContent.includes(text)) {
                            el.style.display = 'none';
                            console.log('🚫 隐藏地区限制消息');
                        }
                    });
                }
            });
        };
        
        // 立即执行一次
        hideMessages();
        
        // 定期检查
        setInterval(hideMessages, 3000);
        
        // 监听DOM变化
        if (document.body) {
            const observer = new MutationObserver(hideMessages);
            observer.observe(document.body, { childList: true, subtree: true });
        }
    }
    
    // 7. 主初始化函数
    function initialize() {
        console.log('🔧 初始化绕过器...');
        
        // 立即执行的拦截
        earlyIntercept();
        interceptNetworkRequests();
        clearRegionCache();
        setupDirectRegister();
        
        // DOM相关的延迟执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                createUI();
                hideRegionMessages();
            });
        } else {
            createUI();
            hideRegionMessages();
        }
        
        // 定期清除缓存
        setInterval(clearRegionCache, 30000);
        
        // 全局调试工具
        window.augmentBypass = {
            config: CONFIG,
            clearCache: clearRegionCache,
            directRegister: window.augmentDirectRegister,
            status: () => {
                console.log('🔍 绕过器状态检查:');
                console.log('- 地理位置伪装: ✅');
                console.log('- 语言伪装: ✅');
                console.log('- 网络拦截: ✅');
                console.log('- GM API: ✅');
                console.log('- 直接注册: ✅');
            }
        };
        
        console.log('✅ Augment 绕过器初始化完成');
        console.log('💡 点击右上角绿色按钮或使用 augmentDirectRegister("email") 直接注册');
    }
    
    // 启动
    initialize();
    
})();
