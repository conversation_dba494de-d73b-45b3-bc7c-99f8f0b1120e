// 弹窗脚本
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const registerBtn = document.getElementById('registerBtn');
    const clearCacheBtn = document.getElementById('clearCacheBtn');
    const result = document.getElementById('result');
    
    // 直接注册
    registerBtn.addEventListener('click', function() {
        const email = emailInput.value.trim();
        
        if (!email) {
            showResult('请输入邮箱地址', false);
            return;
        }
        
        if (!email.includes('@')) {
            showResult('请输入有效的邮箱地址', false);
            return;
        }
        
        registerBtn.textContent = '注册中...';
        registerBtn.disabled = true;
        
        // 发送消息给content script
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'directRegister',
                email: email
            }, function(response) {
                registerBtn.textContent = '直接注册';
                registerBtn.disabled = false;
                
                if (chrome.runtime.lastError) {
                    showResult('扩展通信失败', false);
                    return;
                }
                
                if (response && response.success) {
                    showResult('注册成功！请检查邮箱验证邮件。', true);
                    emailInput.value = '';
                } else {
                    showResult('注册失败，请稍后重试', false);
                }
            });
        });
    });
    
    // 清除缓存
    clearCacheBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'clearCache'
            }, function(response) {
                showResult('缓存已清除', true);
            });
        });
    });
    
    // 显示结果
    function showResult(message, isSuccess) {
        result.textContent = message;
        result.className = 'result ' + (isSuccess ? 'success' : 'error');
        result.style.display = 'block';
        
        setTimeout(() => {
            result.style.display = 'none';
        }, 3000);
    }
    
    // 回车键注册
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            registerBtn.click();
        }
    });
});
