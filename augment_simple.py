#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Code 简化注册脚本
使用方法: python augment_simple.py <EMAIL>
"""

import requests
import json
import sys

def register_augment(email):
    """直接调用Augment Code的注册API"""
    print(f"🚀 开始注册 Augment Code: {email}")
    
    # 简化的请求头
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
    }
    
    # 注册数据
    data = {
        'email': email,
        'connection': 'Username-Password-Authentication',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
    }
    
    # API端点
    url = 'https://login.augmentcode.com/dbconnections/signup'
    
    try:
        print(f"📡 发送请求到: {url}")
        print(f"📦 请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 注册成功！请检查邮箱验证邮件。")
            return True
        else:
            print(f"❌ 注册失败: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"🔍 错误详情: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 原始响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_connection():
    """测试网络连接"""
    print("🔍 测试网络连接...")
    try:
        response = requests.get('https://www.augmentcode.com', timeout=10)
        print(f"✅ 网站可访问，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 网站不可访问: {e}")
        return False

def main():
    print("=" * 50)
    print("🚀 Augment Code 注册工具")
    print("=" * 50)
    
    if len(sys.argv) != 2:
        print("❌ 使用方法: python augment_simple.py <EMAIL>")
        sys.exit(1)
    
    email = sys.argv[1]
    
    if '@' not in email or '.' not in email:
        print("❌ 请输入有效的邮箱地址")
        sys.exit(1)
    
    # 测试连接
    if not test_connection():
        print("💡 建议: 检查网络连接或使用VPN")
        return
    
    # 尝试注册
    success = register_augment(email)
    
    print("=" * 50)
    if success:
        print("🎉 注册流程完成！")
        print("📧 请检查邮箱，点击验证链接完成注册。")
    else:
        print("💡 如果注册失败，可以尝试:")
        print("1. 使用VPN连接美国服务器")
        print("2. 尝试不同的邮箱地址")
        print("3. 检查邮箱格式是否正确")
        print("4. 稍后再试")

if __name__ == "__main__":
    main()
