#!/usr/bin/env python3
"""
最简单的Augment Code测试脚本
"""

import requests
import json

def test_augment_api():
    """测试Augment Code API"""
    
    print("🔍 测试 Augment Code API...")
    
    # 测试邮箱
    email = "<EMAIL>"
    
    # 请求数据 - 添加密码字段
    data = {
        'email': email,
        'password': 'TempPassword123!',  # 临时密码
        'connection': 'Username-Password-Authentication',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
    }
    
    # 请求头
    headers = {
        'Content-Type': 'application/json',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
    }
    
    try:
        # 发送请求
        response = requests.post(
            'https://login.augmentcode.com/dbconnections/signup',
            json=data,
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            print("✅ API调用成功")
        elif response.status_code == 400:
            print("⚠️ 请求格式问题或邮箱已存在")
        elif response.status_code == 403:
            print("🚫 被拒绝 - 可能是地区限制")
        elif response.status_code == 429:
            print("⏳ 请求过于频繁")
        else:
            print(f"❌ 未知状态: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    test_augment_api()
