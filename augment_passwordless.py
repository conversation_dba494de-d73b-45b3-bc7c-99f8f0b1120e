#!/usr/bin/env python3
"""
Augment Code 无密码注册脚本
"""

import requests
import json
import sys

def send_verification_code(email):
    """发送验证码到邮箱"""
    print(f"📧 发送验证码到: {email}")
    
    # 无密码登录请求数据
    data = {
        'email': email,
        'connection': 'email',
        'send': 'code',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 尝试多个可能的端点
    endpoints = [
        'https://login.augmentcode.com/passwordless/start',
        'https://login.augmentcode.com/u/login/passwordless-email',
        'https://auth.augmentcode.com/passwordless/start'
    ]
    
    for endpoint in endpoints:
        try:
            print(f"🔄 尝试端点: {endpoint}")
            
            response = requests.post(
                endpoint,
                json=data,
                headers=headers,
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📄 响应: {response.text}")
            
            if response.status_code == 200:
                print("✅ 验证码发送成功！请检查邮箱。")
                return True
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    if 'description' in error_data:
                        print(f"⚠️ 错误: {error_data['description']}")
                except:
                    print(f"⚠️ 请求错误: {response.text}")
            elif response.status_code == 403:
                print("🚫 被拒绝 - 可能是地区限制")
            
        except Exception as e:
            print(f"❌ 端点 {endpoint} 错误: {e}")
            continue
    
    print("❌ 所有端点都失败了")
    return False

def verify_code(email, code):
    """验证邮箱验证码"""
    print(f"🔐 验证码验证: {email} -> {code}")
    
    data = {
        'email': email,
        'code': code,
        'connection': 'email',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    endpoints = [
        'https://login.augmentcode.com/passwordless/verify',
        'https://login.augmentcode.com/u/login/passwordless-email-challenge',
        'https://auth.augmentcode.com/passwordless/verify'
    ]
    
    for endpoint in endpoints:
        try:
            print(f"🔄 尝试验证端点: {endpoint}")
            
            response = requests.post(
                endpoint,
                json=data,
                headers=headers,
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📄 响应: {response.text}")
            
            if response.status_code == 200:
                print("✅ 验证成功！账户已创建。")
                return True
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    if 'description' in error_data:
                        print(f"⚠️ 错误: {error_data['description']}")
                except:
                    print(f"⚠️ 验证错误: {response.text}")
                    
        except Exception as e:
            print(f"❌ 验证端点 {endpoint} 错误: {e}")
            continue
    
    print("❌ 所有验证端点都失败了")
    return False

def test_website_access():
    """测试网站访问"""
    print("🔍 测试网站访问...")
    try:
        response = requests.get('https://www.augmentcode.com', timeout=10)
        print(f"✅ 网站可访问，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 网站不可访问: {e}")
        return False

def main():
    print("=" * 60)
    print("📧 Augment Code 无密码注册工具")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("1. 发送验证码: python augment_passwordless.py <EMAIL>")
        print("2. 验证码验证: python augment_passwordless.py <EMAIL> 123456")
        print("")
        print("示例:")
        print("python augment_passwordless.py <EMAIL>")
        print("python augment_passwordless.py <EMAIL> 123456")
        sys.exit(1)
    
    email = sys.argv[1]
    
    if '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        sys.exit(1)
    
    # 测试网站访问
    if not test_website_access():
        print("💡 建议: 检查网络连接或使用VPN")
        return
    
    if len(sys.argv) == 2:
        # 只有邮箱，发送验证码
        print(f"📤 步骤1: 发送验证码到 {email}")
        success = send_verification_code(email)
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 验证码已发送！")
            print("📧 请检查你的邮箱（包括垃圾邮件文件夹）")
            print("🔐 收到验证码后，运行以下命令:")
            print(f"python augment_passwordless.py {email} <验证码>")
            print("=" * 60)
        else:
            print("\n💡 如果发送失败，可以尝试:")
            print("1. 使用VPN连接美国服务器")
            print("2. 尝试不同的邮箱地址")
            print("3. 检查邮箱格式是否正确")
            
    elif len(sys.argv) == 3:
        # 有邮箱和验证码，进行验证
        code = sys.argv[2]
        print(f"🔐 步骤2: 验证码验证 {email} -> {code}")
        success = verify_code(email, code)
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 注册成功！")
            print("✅ 你的Augment Code账户已创建")
            print("🌐 现在可以访问 https://app.augmentcode.com 登录")
            print("=" * 60)
        else:
            print("\n💡 如果验证失败，请检查:")
            print("1. 验证码是否正确")
            print("2. 验证码是否已过期")
            print("3. 重新发送验证码")
    else:
        print("❌ 参数错误")

if __name__ == "__main__":
    main()
