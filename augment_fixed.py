#!/usr/bin/env python3
"""
Augment Code 修复版注册脚本
基于实际的网络请求分析
"""

import requests
import json
import sys
import urllib.parse

def send_verification_code(email):
    """发送验证码 - 基于实际的Auth0流程"""
    print(f"📧 发送验证码到: {email}")
    
    # 第一步：获取认证状态
    auth_url = "https://login.augmentcode.com/u/login/identifier"
    params = {
        'state': 'hKFo2SA5Mm5Hci1wRGI0dFltZ09fTVdZdElWWThrWmhqcU9DWqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHlOTkh3d0puUkFkMVBFaUdOVGdTRnRBN3NIV2F6OFJqo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE',
        'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa',
        'protocol': 'oauth2',
        'response_type': 'code',
        'redirect_uri': 'https://app.augmentcode.com/auth/callback',
        'scope': 'openid profile email'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    try:
        # 第一步：访问登录页面获取session
        print("🔄 步骤1: 获取登录页面...")
        response = session.get(auth_url, params=params, timeout=15)
        print(f"📊 登录页面状态: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面: {response.status_code}")
            return False
        
        # 第二步：提交邮箱地址
        print("🔄 步骤2: 提交邮箱地址...")
        
        # 从页面中提取必要的参数（简化版）
        login_data = {
            'state': params['state'],
            'username': email,
            'js-available': 'true',
            'webauthn-available': 'true',
            'is-brave': 'false',
            'webauthn-platform-available': 'true',
            'action': 'default'
        }
        
        # 提交邮箱
        submit_url = "https://login.augmentcode.com/u/login/identifier"
        response = session.post(
            submit_url,
            data=login_data,
            allow_redirects=True,
            timeout=15
        )
        
        print(f"📊 邮箱提交状态: {response.status_code}")
        print(f"📄 响应URL: {response.url}")
        
        # 检查是否重定向到验证码页面
        if 'passwordless-email' in response.url or 'challenge' in response.url:
            print("✅ 成功！已重定向到验证码页面")
            print("📧 验证码应该已发送到你的邮箱")
            return True
        elif response.status_code == 200:
            print("⚠️ 页面加载成功，但可能需要手动操作")
            print("💡 请检查是否收到验证码邮件")
            return True
        else:
            print(f"❌ 邮箱提交失败: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def try_direct_api():
    """尝试直接API调用"""
    print("🔄 尝试直接API调用...")
    
    # 基于之前抓取的请求，尝试直接调用
    endpoints_to_try = [
        {
            'url': 'https://login.augmentcode.com/co/authenticate',
            'method': 'POST',
            'data': {
                'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa',
                'credential_type': 'http://auth0.com/oauth/grant-type/passwordless/otp',
                'realm': 'email',
                'username': '<EMAIL>'
            }
        },
        {
            'url': 'https://login.augmentcode.com/passwordless/start',
            'method': 'POST',
            'data': {
                'client_id': 'wlLTVWGDfItW9HziIowSRieQNRylMPTa',
                'connection': 'email',
                'email': '<EMAIL>',
                'send': 'code'
            }
        }
    ]
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'X-Forwarded-For': '*******',
        'CF-IPCountry': 'US',
        'Origin': 'https://login.augmentcode.com',
        'Referer': 'https://login.augmentcode.com/'
    }
    
    for endpoint in endpoints_to_try:
        try:
            print(f"🔄 尝试: {endpoint['url']}")
            
            response = requests.post(
                endpoint['url'],
                json=endpoint['data'],
                headers=headers,
                timeout=10
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📄 响应: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("✅ API调用成功！")
                return True
                
        except Exception as e:
            print(f"❌ 端点失败: {e}")
            continue
    
    return False

def main():
    print("=" * 60)
    print("🔧 Augment Code 修复版注册工具")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python augment_fixed.py <EMAIL>")
        sys.exit(1)
    
    email = sys.argv[1]
    
    if '@' not in email:
        print("❌ 请输入有效的邮箱地址")
        sys.exit(1)
    
    print(f"📧 目标邮箱: {email}")
    
    # 方法1: 模拟浏览器流程
    print("\n🌐 方法1: 模拟浏览器注册流程")
    success1 = send_verification_code(email)
    
    if success1:
        print("\n✅ 浏览器流程成功！请检查邮箱验证码。")
        return
    
    # 方法2: 直接API调用
    print("\n🔧 方法2: 尝试直接API调用")
    success2 = try_direct_api()
    
    if not success1 and not success2:
        print("\n💡 所有方法都失败了，建议:")
        print("1. 使用VPN连接美国服务器")
        print("2. 直接在浏览器中访问 https://login.augmentcode.com")
        print("3. 手动注册并观察网络请求")
        print("4. 尝试不同的邮箱地址")

if __name__ == "__main__":
    main()
